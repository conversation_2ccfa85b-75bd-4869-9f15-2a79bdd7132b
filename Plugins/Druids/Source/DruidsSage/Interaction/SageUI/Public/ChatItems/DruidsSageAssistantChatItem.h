#pragma once

#include <CoreMinimal.h>
#include <Blueprint/UserWidget.h>

#include "DruidsSageChatTypes.h"
#include "IDruidsSageChatItem.h"

#include "DruidsSageAssistantChatItem.generated.h"

class UDruidsSageMessagingHandler;
class UTextBlock;
class UVertical<PERSON>ox;
class USc<PERSON>Box;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnActionAppliedAssistantUMG, const FString&, JsonString);

UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageAssistantChatItem : public UIDruidsSageChatItem
{
	GENERATED_BODY()

public:
	UDruidsSageAssistantChatItem(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void SynchronizeProperties() override;
	// End of UUserWidget interface

	// UIDruidsSageChatItem interface implementation
	virtual FName GetTypeNameCpp() const override;
	virtual void FillInDruidsMessageCpp(FDruidsSageChatMessage& Message) const override;
	virtual EDruidsSageChatRole GetMessageRoleCpp() const override;
	virtual TWeakObjectPtr<UDruidsSageMessagingHandler> GetMessagingHandlerCpp() const override;
	virtual void UpdateFromContentJsonCpp(const TSharedPtr<FJsonValue>& ContentJson) override;

	// Initialization methods
	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void InitializeAssistantChatItem();

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void SetScrollBoxReference(UScrollBox* InScrollBox);

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void SetRawText(const FString& ContentText);

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Chat Item")
	FOnActionAppliedAssistantUMG OnActionApplied;

	static FName GetClassName() { return "UDruidsSageAssistantChatItem"; }

protected:
	// BindWidget properties for Blueprint binding
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UTextBlock* RoleWidget;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UVerticalBox* MessageContainer;

private:
	TWeakObjectPtr<UDruidsSageMessagingHandler> MessagingHandler;
	TSharedPtr<FJsonValue> PreviousContentJson;
	TArray<UIDruidsSageChatItem*> ChildChatItems;

	void SetupMessagingHandler();
	void SetupWidgets();
	void UpdateFromDruidsSageMessage(const FDruidsSageChatMessage* Message);
	void UpdateFromThinkingJson(const TArray<TSharedPtr<FJsonValue>>& ThinkingArray);
	UIDruidsSageChatItem* CreateNewChatItem(const TSharedPtr<FJsonValue>& ContentJson);
	void UpdateChatItem(UIDruidsSageChatItem* ChatItem, const TSharedPtr<FJsonValue>& ContentJson);

	UFUNCTION()
	void HandleActionRequestApplied(const FString& JsonString);

	// Static helper functions
	static FString GetContentType(const TSharedPtr<FJsonValue>& ContentJson);
	static TSharedPtr<FJsonValueArray> CreateTextContentJson(const FString& Content);
	static FString CreateProcessingText();
	static bool ChatItemMatchesType(UIDruidsSageChatItem* Widget, const FString& Type);
};
